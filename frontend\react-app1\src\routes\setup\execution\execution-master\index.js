import React, { useState, useEffect, useCallback } from 'react';
import { Select, Alert, Form, Button, Row, Card, Spin, message } from 'antd';
import Widget from '../../../../components/Widget';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import { convertDateFieldsToMoments } from '../../../../util/helpers';
import EditablePagedApiTable from '../../../../components/wify-utils/EditablePagedApiTable';
import AppModuleHeader from '../../../../components/AppModuleHeader';
import { render } from '@testing-library/react';

const protoUrl = '/setup/automation-deployment/execution-master/proto';
const subtmitUrl = '/setup/automation-deployment/execution-master';
const listUrl = '/setup/automation-deployment/execution-master/list';

const ExecutionMaster = () => {
    const [viewData, setViewData] = useState(undefined);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);
    const [error, setError] = useState(undefined);
    const [form] = Form.useForm();
    const forceUpdate = FormBuilder.useForceUpdate();
    const [renderHelper, setRenderHelper] = useState(true);
    const [selectedBrand, setSelectedBrand] = useState(undefined);

    // EditablePagedApiTable state
    const [tableSearchQuery, setTableSearchQuery] = useState('');
    const [tableFilterObject, setTableFilterObject] = useState({});
    const [tableData, setTableData] = useState([]);
    const [totalRecords, setTotalRecords] = useState(0);

    //const [isPrefillProcessed, setIsPrefillProcessed] = useState(false);

    useEffect(() => {
        initViewData();
    }, []);

    // Effect to handle prefill data and trigger brand options fetch (only once)
    useEffect(() => {
        if (viewData && viewData.execution_master_data && !viewData.org_list) {
            const prefillData = viewData.execution_master_data[0].form_data;

            if (prefillData && prefillData.vertical_list) {
                // Set the form values first
                form.setFieldsValue(prefillData);
                // Then fetch brand options for the selected vertical
                fetchBrandOptions(prefillData.vertical_list);
                forceUpdate();
            }
        }
    }, [viewData]);

    // Search is now handled by EditablePagedApiTable

    // Old brand table functions removed - now using EditablePagedApiTable

    const fetchBrandOptions = (verticalId) => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            setError(undefined);
            var params = {};
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
                forceUpdate();
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(
                protoUrl + '/' + verticalId,
                params,
                onComplete,
                onError
            );
        }
    };

    const initViewData = () => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            setViewData(undefined);
            setError(undefined);
            var params = {};
            const onComplete = (resp) => {
                setIsLoadingViewData(false);
                setViewData(resp.data);
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performGetCall(protoUrl, params, onComplete, onError);
        }
    };

    const handleFinish = (values) => {
        if (!isFormSubmitting) {
            setIsFormSubmitting(true);
            console.log('values', values);
            // Include table data in the submission
            var params = values;

            const onComplete = (resp) => {
                setIsFormSubmitting(false);
                message.success('Saved successfully');
            };
            const onError = (error) => {
                setIsFormSubmitting(false);
                message.error(http_utils.decodeErrorToMessage(error));
            };
            http_utils.performPostCall(subtmitUrl, params, onComplete, onError);
        }
    };
    // Old search functions removed - now using EditablePagedApiTable's built-in search

    // EditablePagedApiTable handlers
    const handleTableSearchChange = useCallback((value) => {
        setTableSearchQuery(value);
    }, []);

    const handleTableDataChange = useCallback((newData) => {
        console.log('Table data changed:', newData);
        form.setFieldsValue({ execution_master_table_meta: newData });
        setTableData(newData);
    }, []);

    const handleApiRespChange = useCallback((apiResponse) => {
        // Handle API response if needed
        console.log('API Response:', apiResponse);
    }, []);

    const handleTotalUpdated = useCallback((total) => {
        setTotalRecords(total);
    }, []);

    const handleRowClick = useCallback((rowData) => {
        console.log('Row clicked:', rowData);
        // Handle row click if needed
    }, []);

    // Column metadata for EditablePagedApiTable
    const getEditableTableColMeta = () => {
        return [
            {
                key: 'service_type_name',
                label: 'Brand/Service Type',
                widget: 'input',
                widgetProps: {
                    disabled: true,
                },
            },
            {
                key: 'product_details',
                label: 'Product Details',
                widget: 'input',
                widgetProps: {
                    disabled: true,
                },
            },
            {
                key: 'skill_1',
                label: 'Skill 1',
                widget: 'select',
                dataIndex: 'skill_1_id',
                options: viewData?.vertical_skill_list || [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                    labelInValue: false, // important → value will just be the ID
                },
                
            },
            {
                key: 'manpower_1',
                label: 'Manpower 1',
                widget: 'number',
                widgetProps: {
                    min: 0,
                },
            },
            {
                key: 'duration_1',
                label: 'Duration 1',
                widget: 'number',
            },
            {
                key: 'skill_2',
                label: 'Skill 2',
                widget: 'select',
                options: viewData?.vertical_skill_list || [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
            },
            {
                key: 'manpower_2',
                label: 'Manpower 2',
                widget: 'number',
                widgetProps: {
                    min: 0,
                },
            },
            {
                key: 'duration_2',
                label: 'Duration 2',
                widget: 'number',
            },
            {
                key: 'skill_3',
                label: 'Skill 3',
                widget: 'select',
                options: viewData?.vertical_skill_list || [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
            },
            {
                key: 'manpower_3',
                label: 'Manpower 3',
                widget: 'number',
                widgetProps: {
                    min: 0,
                },
            },
            {
                key: 'duration_3',
                label: 'Duration 3',
                widget: 'number',
            },
        ];
    };

    const getMeta = () => {
        const verticalList = viewData?.vertical_list;
        const selectedVertical = form?.getFieldValue('vertical_list');
        const selectedBrand = form?.getFieldValue('brand');
        const meta = {
            formItemLayout: null, // Must set this for inline layout
            colon: true,
            fields: [
                {
                    key: 'vertical_list',
                    label: 'Select verticals',
                    widget: 'select',
                    widgetProps: {
                        mode: 'single',
                        optionFilterProp: 'children',
                    },
                    onChange: (value) => {
                        console.log('Vertical changed to:', value);

                        // Update table filter
                        setTableFilterObject((prev) => ({
                            ...prev,
                            vertical_id: value,
                            service_type_id: undefined, // Clear service type filter when vertical changes
                        }));

                        forceUpdate();
                        form.setFieldsValue({ brand: undefined });
                        fetchBrandOptions(value);
                    },
                    colSpan: 4,
                    options: verticalList || [],
                },

                ...(selectedVertical
                    ? [
                          {
                              key: 'brand',
                              label: 'Select Brand/Service',
                              widget: 'select',
                              widgetProps: {
                                  mode: 'multiple',
                                  optionFilterProp: 'children',
                                  placeholder: 'Select one or more brands',
                              },
                              options: viewData?.org_list || [],
                              colSpan: 4,
                              onChange: (value) => {
                                  setSelectedBrand(value);
                                  // Update table filter to refresh data
                                  setTableFilterObject((prev) => ({
                                      ...prev,
                                      service_type_id: value,
                                  }));
                                  forceUpdate();
                              },
                          },
                      ]
                    : []),
                {
                    key: 'execution_master_table_meta',
                    className: 'gx-d-none',
                    widgetProps: {
                        hidden: true,
                    },
                },

                ...(selectedBrand
                    ? [
                          {
                              key: 'execution_master_editable_table',
                              render: () => {
                                  return (
                                      <>
                                          <AppModuleHeader
                                              placeholder={
                                                  'Search by product name or sku...'
                                              }
                                              currValue={tableSearchQuery}
                                              onChange={(e) =>
                                                  handleTableSearchChange(e)
                                              }
                                              tellParentToRefreshList={() =>
                                                  setRenderHelper(!renderHelper)
                                              }
                                          />
                                          <EditablePagedApiTable
                                              // API and Search Props
                                              dataSourceApi={listUrl}
                                              searchQuery={tableSearchQuery}
                                              filterObject={{
                                                  ...tableFilterObject,
                                                  vertical_id: selectedVertical,
                                                  ...(selectedBrand &&
                                                  selectedBrand.length > 0
                                                      ? {
                                                            service_type_id:
                                                                selectedBrand,
                                                        }
                                                      : {}),
                                              }}
                                              //filterObject={{}}
                                              pageSize={20}
                                              onApiRespChange={
                                                  handleApiRespChange
                                              }
                                              onTotalUpdated={
                                                  handleTotalUpdated
                                              }
                                              onRowClick={handleRowClick}
                                              // Editable Table Props
                                              colMeta={getEditableTableColMeta()}
                                              edittable={false}
                                              onDataChange={
                                                  handleTableDataChange
                                              }
                                              // Table Display Props
                                              bordered={true}
                                              tableSize="small"
                                              overFlowScrollBar={true}
                                              // Action Column Props
                                              hideActionBtn={true}
                                              // Other Props
                                              readOnly={false}
                                              noFilters={true}
                                              paginationSize="small"
                                          />
                                      </>
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };

        return meta;
    };

    const meta = getMeta();

    const prefillFormData = convertDateFieldsToMoments(
        viewData?.execution_master_data?.[0]?.form_data || {},
        meta.fields
    );
    return (
        <div>
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData == undefined ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <Widget>
                    <>
                        <h2 className="gx-my-1">Execution Master</h2>
                        <Alert
                            message="Configure execution parameters for your brand-service combinations"
                            type="info"
                            showIcon
                        />
                        <Form
                            form={form}
                            layout="vertical"
                            onFinish={handleFinish}
                            initialValues={prefillFormData}
                        >
                            <FormBuilder meta={meta} form={form} />

                            <Form.Item
                                className="form-footer"
                                style={{ marginTop: 24 }}
                            >
                                {isFormSubmitting && <Spin />}
                                <Button
                                    htmlType="submit"
                                    type="primary"
                                    className="gx-mb-0"
                                    disabled={isFormSubmitting}
                                >
                                    Submit
                                </Button>
                            </Form.Item>
                        </Form>

                        {/* {selectedBrand && selectedBrand.length > 0 && (
                            <>
                                <AppModuleHeader
                                    placeholder={
                                        'Search by product name or sku...'
                                    }
                                    currValue={tableSearchQuery}
                                    onChange={(e) => handleTableSearchChange(e)}
                                    tellParentToRefreshList={() =>
                                        setRenderHelper(!renderHelper)
                                    }
                                />
                                <EditablePagedApiTable
                                    // API and Search Props
                                    dataSourceApi={listUrl}
                                    searchQuery={tableSearchQuery}
                                    filterObject={{
                                        ...tableFilterObject,
                                        vertical_id:
                                            form.getFieldValue('vertical_list'),
                                        ...(selectedBrand &&
                                        selectedBrand.length > 0
                                            ? {
                                                  service_type_id:
                                                      selectedBrand,
                                              }
                                            : {}),
                                    }}
                                    //filterObject={{}}
                                    pageSize={20}
                                    onApiRespChange={handleApiRespChange}
                                    onTotalUpdated={handleTotalUpdated}
                                    onRowClick={handleRowClick}
                                    // Editable Table Props
                                    colMeta={getEditableTableColMeta()}
                                    edittable={false}
                                    onDataChange={handleTableDataChange}
                                    // Table Display Props
                                    bordered={true}
                                    tableSize="small"
                                    overFlowScrollBar={true}
                                    // Action Column Props
                                    hideActionBtn={true}
                                    // Other Props
                                    readOnly={false}
                                    noFilters={true}
                                    paginationSize="small"
                                />
                            </>
                        )} */}
                    </>
                </Widget>
            )}
        </div>
    );
};

export default ExecutionMaster;
