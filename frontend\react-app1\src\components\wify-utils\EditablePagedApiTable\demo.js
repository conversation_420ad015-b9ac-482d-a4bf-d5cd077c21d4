import React, { useState } from 'react';
import { Input, Select, Button, Card, Space, Switch } from 'antd';
import EditablePagedApiTable from './index';

const { Search } = Input;
const { Option } = Select;

// Static demo data


const EditablePagedApiTableDemo = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [filterObject, setFilterObject] = useState({});
    const [tableData, setTableData] = useState([]);


    // Example column metadata combining both search/API and editable functionality
    const colMeta = [
        {
            key: 'id',
            label: 'ID',
            widget: 'input',
            widgetProps: {
                disabled: true, // ID should not be editable
            },
        },
        {
            key: 'name',
            label: 'Name',
            widget: 'input',
            widgetProps: {
                placeholder: 'Enter name',
            },
        },
        {
            key: 'email',
            label: 'Email',
            widget: 'input',
            widgetProps: {
                placeholder: 'Enter email',
                type: 'email',
            },
        },
       
        {
            key: 'status',
            label: 'Status',
            widget: 'select',
            options: [
                { label: 'Active', value: 'active' },
                { label: 'Inactive', value: 'inactive' },
                { label: 'Pending', value: 'pending' },
            ],
            widgetProps: {
                placeholder: 'Select status',
            },
        },
        {
            key: 'role',
            label: 'Role',
            widget: 'select',
            options: [
                { label: 'Admin', value: 'admin' },
                { label: 'User', value: 'user' },
                { label: 'Manager', value: 'manager' },
            ],
            widgetProps: {
                placeholder: 'Select role',
            },
        },
        {
            key: 'salary',
            label: 'Salary',
            widget: 'number',
            widgetProps: {
                placeholder: 'Enter salary',
                min: 0,
                formatter: (value) =>
                    `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ','),
                parser: (value) => value.replace(/\$\s?|(,*)/g, ''),
            },
        },
        {
            key: 'join_date',
            label: 'Join Date',
            widget: 'date',
            widgetProps: {
                placeholder: 'Select join date',
            },
        },
    ];

    // Handle search input change
    const handleSearchChange = (value) => {
        setSearchQuery(value);
    };

    // Handle filter changes
    const handleFilterChange = (key, value) => {
        setFilterObject((prev) => ({
            ...prev,
            [key]: value,
        }));
    };

    // Handle data changes from the table
    const handleDataChange = (newData) => {
        setTableData(newData);
        console.log('Table data changed:', newData);
    };

    // Handle API response changes
    const handleApiRespChange = (apiResponse) => {
        console.log('API response:', apiResponse);
    };

    // Handle row click
    const handleRowClick = (rowData) => {
        console.log('Row clicked:', rowData);
    };

    // Handle total count update
    const handleTotalUpdated = (total) => {
        console.log('Total records:', total);
    };

    // Save/Submit handler for the edited data
    const handleSaveData = () => {
        console.log('Saving data:', tableData);
        // Here you would typically make an API call to save the data
        // Example: http_utils.performPostCall('/api/save-data', tableData, onSuccess, onError);
    };

    return (
        <div style={{ padding: '20px' }}>
            <Card
                title="Editable Paged API Table Demo"
                style={{ marginBottom: '20px' }}
            >
                <Space direction="vertical" style={{ width: '100%' }}>
                    

                    {/* Search and Filter Controls */}
                    <div
                        style={{
                            display: 'flex',
                            gap: '10px',
                            marginBottom: '20px',
                            flexWrap: 'wrap',
                        }}
                    >
                        <Search
                            placeholder="Search by name, email, or department..."
                            allowClear
                            style={{ width: 300 }}
                            onSearch={handleSearchChange}
                            onChange={(e) => handleSearchChange(e.target.value)}
                        />

                        <Select
                            placeholder="Filter by Status"
                            allowClear
                            style={{ width: 150 }}
                            onChange={(value) =>
                                handleFilterChange('status', value)
                            }
                        >
                            <Option value="active">Active</Option>
                            <Option value="inactive">Inactive</Option>
                            <Option value="pending">Pending</Option>
                        </Select>

                        <Select
                            placeholder="Filter by Role"
                            allowClear
                            style={{ width: 150 }}
                            onChange={(value) =>
                                handleFilterChange('role', value)
                            }
                        >
                            <Option value="admin">Admin</Option>
                            <Option value="user">User</Option>
                            <Option value="manager">Manager</Option>
                        </Select>

                       

                        <Button type="primary" onClick={handleSaveData}>
                            Save Changes
                        </Button>
                    </div>

                    {/* The Hybrid Table Component */}
                    <EditablePagedApiTable
                        // API and Search Props (from PagedApiListView)
                        dataSourceApi={'/api/users'} // Use null for static data
                        searchQuery={searchQuery}
                        filterObject={filterObject}
                        pageSize={5} // Smaller page size for demo
                        onApiRespChange={handleApiRespChange}
                        onTotalUpdated={handleTotalUpdated}
                        onRowClick={handleRowClick}
                        // Static Data Props
                       
                        // Editable Table Props (from MetaInputTable)
                        colMeta={colMeta}
                        edittable={true}
                        onDataChange={handleDataChange}
                        // Table Display Props
                        bordered={true}
                        tableSize="middle"
                        overFlowScrollBar={true}
                        // Action Column Props
                        hideActionBtn={false} // Set to true to hide add/remove buttons
                        actionColFixed={true} // Fix action column to right
                        // Other Props
                        readOnly={false} // Set to true to make table read-only
                        noFilters={false} // Set to true to disable column filters
                    
                    />
                </Space>
            </Card>

            {/* Display current table data for debugging */}
            <Card
                title="Current Table Data (Debug)"
                style={{ marginTop: '20px' }}
            >
                <pre
                    style={{
                        background: '#f5f5f5',
                        padding: '10px',
                        borderRadius: '4px',
                    }}
                >
                    {JSON.stringify(tableData, null, 2)}
                </pre>
            </Card>
        </div>
    );
};

export default EditablePagedApiTableDemo;
