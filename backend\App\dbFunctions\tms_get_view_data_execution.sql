CREATE OR REPLACE FUNCTION public.tms_get_view_data_execution(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
	declare
		status boolean;
		message text;
		resp_data json default '{}'::json;
		pulse_tracker_fields json;
		vertical_list json;
		org_id_ int;
        vertical_id_ int;
        org_list json;
        vertical_skill_list json;
        srvc_type_id_ int[];
        category_wise_product_list json;
        execution_master_data json;

	BEGIN
		status = false;
		message = 'Internal_error';
		
		org_id_ = form_data->>'org_id';
	    vertical_id_ = form_data->>'vertical_id';
        srvc_type_id_ = tms_hlpr_get_srvc_type_ids_by_verticals(ARRAY[vertical_id_],org_id_)::int[];
	
		vertical_list = tms_hlpr_get_verticals_list(org_id_);
	   if vertical_id_ = 0 then 
	        execution_master_data = array_to_json(array(
	            select jsonb_build_object(
	                   'form_data',execution_master.form_data,
	                   'srvc_type_id',execution_master.service_type_id,
	                   'sku_id',execution_master.sku_id,
	                   'vertical_id',execution_master.vertical_id
	                )
	            from public.execution_master  as execution_master
	            where execution_master.org_id = org_id_
	            -- and execution_master.vertical_id = vertical_id_
	           -- order by execution_master.id  desc
	            limit 1
	        ));
	        if json_array_length(execution_master_data) > 0 then
	            resp_data = jsonb_set(resp_data::jsonb,'{execution_master_data}'::text[],execution_master_data::jsonb,true);
	        end if;
	    end if;   
	    if vertical_id_ > 0 then
			org_list = array_to_json(array(
                select jsonb_build_object(
                        'value',service_types.service_type_id,
                        'label',orgs.nickname || ' - ' || service_types.title
                       
                    )
                 from cl_tx_orgs_settings as org_settings
                inner join cl_cf_service_types as service_types 
                   on service_types.service_type_id = any(
                                    array(
                                        select json_array_elements_text(org_settings.settings_data->'srvc_type_id')
                                    )::int[]
                                )
                inner join cl_tx_orgs as orgs 
                   on orgs.org_id = service_types.org_id 
                where org_settings.db_id = vertical_id_
                group by orgs.org_id, service_types.service_type_id 	
                order by orgs.nickname asc
		));	
            if json_array_length(org_list) > 0 then
	   	    resp_data = jsonb_set(resp_data::jsonb,'{org_list}'::text[],org_list::jsonb,true);
            end if;

            vertical_skill_list = array_to_json(array(
                              select jsonb_build_object(
                                        'value',skill.db_id,
                                        'label',skill.skill_name
                                    )
                                from cl_tx_skills as skill
                               inner join cl_tx_skill_map as skill_map
                                  on skill_map.skill_id = skill.db_id
                               where skill_map.vertical_id = vertical_id_
                               group by skill.db_id
                               order by skill.skill_name asc
            ));
            if json_array_length(vertical_skill_list) > 0 then
                resp_data = jsonb_set(resp_data::jsonb,'{vertical_skill_list}'::text[],vertical_skill_list::jsonb,true);
            end if;
                   
        end if;
		-- now need to get vertical skill list , category with product list
       
	
		status = true;
		message = 'success';
		
		resp_data = jsonb_set(resp_data::jsonb,'{vertical_list}'::text[],vertical_list::jsonb,true);
		return json_build_object('status',status,'code',message,'data',resp_data);
	END;
$function$
;
