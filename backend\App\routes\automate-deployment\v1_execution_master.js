var express = require('express');
var router = express.Router();
var { getUserContextFrmReq } = require('../../api_models/utils/authrizor');

//proto route
router.get('/proto/:entry_id?', function (req, res, next) {
    console.log('calllllllllllll');
    const entry_id = req.params.entry_id;
    const model = setParamsToModel(req);
    model.getViewData(req.query, entry_id).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/list', function (req, res, next) {
    const model = setParamsToModel(req);
    model.getExecutionMasterList(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

//post route
router.post('/', function (req, res, next) {
    console.log('calllllllllllll');
    console.log('req.body=>>>>>>>>', req.body);
    const model = setParamsToModel(req);
    model.createOrUpdate(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

const setParamsToModel = (req) => {
    const model = require('../../api_models/setup/automate-deployment/execution_master_model');
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    return model.getFreshInstance(model);
};

module.exports = router;
