import { MinusCircleFilled, PlusCircleFilled } from '@ant-design/icons';
import { Alert, Button, Input, Table, Form, Spin, Pagination } from 'antd';
import FormBuilder from 'antd-form-builder';
import Column from 'antd/lib/table/Column';
import React, { Component, useEffect } from 'react';
import { generateUUID } from '../../../util/helpers';
import http_utils from '../../../util/http_utils';

const getRandomuserParams = (params) => ({
    results: params.pagination.pageSize,
    page: params.pagination.page,
    ...params,
});
const CellFormField = ({ record, cellFieldMeta }) => {
    const [form] = Form.useForm();
    useEffect(() => {
        // console.log('record[cellFieldMeta.key]',record[cellFieldMeta.key])
        if (typeof record[cellFieldMeta.key] != 'object') {
            form.setFieldsValue(record);
        }
        // if()
    }, [record]);
    return (
        <Form initialValues={record} form={form}>
            <FormBuilder meta={cellFieldMeta} form={form} />
        </Form>
    );
};

export default class EditablePagedApiTable extends Component {
    initState = {
        data: [],
        pagination: {
            current: 1,
            pageSize: this.props.pageSize || 10,
        },
        loading: false,
        render_helper: false,
        render_refreshed: true,
    };

    state = this.initState;

    constructor(props) {
        super(props);
    }

    componentDidMount() {
        const { pagination } = this.state;
        if (this.props.staticData) {
            this.loadStaticData();
        } else {
            this.fetch({ pagination });
        }
    }

    componentDidUpdate(prevProps) {
        // Handle static data changes
        if (prevProps.staticData !== this.props.staticData) {
            if (this.props.staticData) {
                this.loadStaticData();
            } else {
                this.fetch({ pagination: this.state.pagination });
            }
            return;
        }

        // Refetch data when search query or filters change (only for API mode)
        if (
            !this.props.staticData &&
            (prevProps.searchQuery !== this.props.searchQuery ||
                JSON.stringify(prevProps.filterObject) !==
                    JSON.stringify(this.props.filterObject) ||
                JSON.stringify(prevProps.extraFilterObject) !==
                    JSON.stringify(this.props.extraFilterObject))
        ) {
            this.fetch({ pagination: this.state.pagination });
        }

        // Handle static data filtering
        if (
            this.props.staticData &&
            (prevProps.searchQuery !== this.props.searchQuery ||
                JSON.stringify(prevProps.filterObject) !==
                    JSON.stringify(this.props.filterObject))
        ) {
            this.loadStaticData();
        }
    }

    loadStaticData = () => {
        if (!this.props.staticData) return;

        let filteredData = [...this.props.staticData];
        const { searchQuery, filterObject } = this.props;
        const { pagination } = this.state;

        // Apply search filter
        if (searchQuery && searchQuery.trim()) {
            const query = searchQuery.toLowerCase();
            filteredData = filteredData.filter((item) =>
                Object.values(item).some(
                    (value) =>
                        value && value.toString().toLowerCase().includes(query)
                )
            );
        }

        // Apply column filters
        if (filterObject) {
            Object.keys(filterObject).forEach((key) => {
                if (filterObject[key]) {
                    filteredData = filteredData.filter(
                        (item) => item[key] === filterObject[key]
                    );
                }
            });
        }

        // Add unique IDs to data for editable functionality
        const processedData = filteredData.map((item, index) => {
            if (!item.input_table_id) {
                item.input_table_id = generateUUID();
            }
            item.key = item.input_table_id;
            return item;
        });

        // Apply pagination
        const startIndex = (pagination.current - 1) * pagination.pageSize;
        const endIndex = startIndex + pagination.pageSize;
        const paginatedData = processedData.slice(startIndex, endIndex);

        this.setState(
            {
                loading: false,
                data: paginatedData,
                pagination: {
                    ...pagination,
                    total: processedData.length,
                },
            },
            () => {
                if (this.props.onTotalUpdated) {
                    this.props.onTotalUpdated(processedData.length);
                }
                if (this.props.onDataChange) {
                    this.props.onDataChange(paginatedData);
                }
            }
        );
    };

    handlePaginationChange = (page, pageSize) => {
        this.setState({
            pagination: {
                current: page,
                pageSize: pageSize,
            },
        });

        if (this.props.staticData) {
            // For static data, just reload with new pagination
            setTimeout(() => this.loadStaticData(), 0);
        } else {
            // For API data, fetch from server
            this.fetch({
                pagination: {
                    current: page,
                    pageSize: pageSize,
                },
            });
        }
    };

    handleTableChange = (pagination, filters, sorter) => {
        this.fetch({
            pagination,
            ...filters,
        });
    };

    fetch = (params = {}) => {
        // If no API endpoint provided (static data mode), don't fetch
        if (!this.props.dataSourceApi) {
            return;
        }

        params = {
            ...params,
            filters: this.props.filterObject,
            search_query: this.props.searchQuery ? this.props.searchQuery : '',
        };

        if (this.props.extraFilterObject) {
            params['filters'] = {
                ...params?.filters,
                ...this.props?.extraFilterObject,
            };
        }
        if (this.props.extraParams) {
            params = { ...params, ...this.props.extraParams };
        }

        this.setState({ loading: true });

        const onSuccess = (data) => {
            if (this.props.onApiRespChange) {
                this.props.onApiRespChange(data.data);
            }

            // Add unique IDs to data for editable functionality
            const processedData = data.data.data.map((item, index) => {
                if (!item.input_table_id) {
                    item.input_table_id = generateUUID();
                }
                item.key = item.input_table_id;
                return item;
            });

            this.setState(
                {
                    loading: false,
                    data: processedData,
                    pagination: {
                        ...this.state.pagination,
                        ...data.data.pagination,
                    },
                    api_resp: data.data,
                },
                () => {
                    if (this.props.onTotalUpdated) {
                        this.props.onTotalUpdated(this.state.pagination?.total);
                    }
                    if (this.props.onDataChange) {
                        this.props.onDataChange(processedData);
                    }
                }
            );
        };

        const onError = (error) => {
            this.setState({
                loading: false,
                data: [],
            });
        };

        http_utils.performGetCall(
            this.props.dataSourceApi,
            getRandomuserParams(params),
            onSuccess,
            onError
        );
    };

    getDataFrTable = () => {
        console.log('getDataFrTable', this.state.data);
        return this.state.data;
        //static data create
        // return [
        //     {
        //         key: '1',
        //         name: 'John Brown',
        //         age: 32,
        //         address: 'New York No. 1 Lake Park',
        //     },
        //     {
        //         key: '2',
        //         name: 'Jim Green',
        //         age: 42,
        //         address: 'London No. 1 Lake Park',
        //     },
        // ];
    };

    getColMeta = () => {
        console.log('getColMeta', this.props.colMeta);
        return this.props.colMeta || [];
        // return [
        //    {
        //        key: 'name',
        //        label: 'Name',
        //        widget: 'input',
        //        widgetProps: { placeholder: 'Enter name' },
        //    },
        //    {
        //        key: 'age',
        //        label: 'Age',
        //        widget: 'number',
        //        widgetProps: { min: 0 },
        //    },
        // ];
    };

    refresh = (tableRefresh) => {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    };

    onEdittableControlClick(record, table_index, increment) {
        const newData = [...this.getDataFrTable()];
        let index = table_index;

        newData.forEach((singleRow, i) => {
            if (singleRow.input_table_id === record.input_table_id) {
                index = i;
            }
        });

        if (increment > 0) {
            // Add new row
            for (let i = 0; i < increment; i++) {
                const newRow = {
                    input_table_id: generateUUID(),
                    key: generateUUID(),
                };
                newData.splice(index + 1, 0, newRow);
            }
        } else {
            // Delete current row
            if (this.getDataFrTable().length > 0) {
                newData.splice(index, 1);
                // If newData is empty after deletion add an empty object
                if (newData.length === 0) {
                    const emptyRow = {
                        input_table_id: generateUUID(),
                        key: generateUUID(),
                    };
                    newData.push(emptyRow);
                }
            }
        }

        this.setState({ data: newData }, () => {
            if (this.props.onDataChange) {
                this.props.onDataChange(newData);
            }
        });
        this.refresh(true);
    }

    getOverFlowScrollBarProps = () => {
        if (this.props.overFlowScrollBar) {
            return { x: 'max-content' };
        }
        return {};
    };

    tableOnRowClick = (singleRowData) => {
        if (this.props.onRowClick) {
            this.props.onRowClick(singleRowData);
        }
    };

    render() {
        const { data, pagination, loading, api_resp, render_refreshed } =
            this.state;
        const { demoMode, readOnly, edittable, hideActionBtn, actionColFixed } =
            this.props;

        return (
            <div>
                {demoMode && <Alert message="Running in DEMO MODE!!" />}

                {!render_refreshed && (
                    <div className="gx-text-center">
                        <Spin />
                    </div>
                )}

                {render_refreshed && (
                    <Table
                        className="wy-cursor-pointer"
                        dataSource={data}
                        pagination={false}
                        loading={loading}
                        onRow={(singleRowData) => ({
                            onClick: () => this.tableOnRowClick(singleRowData),
                        })}
                        scroll={this.getOverFlowScrollBarProps()}
                        bordered={this.props?.bordered || true}
                        sticky={this.props?.sticky || false}
                        size={this.props?.tableSize || 'middle'}
                        rowSelection={this.props.rowSelection || null}
                        rowClassName={(record) =>
                            record?.is_restricted_view
                                ? 'wy-restricted-table-row'
                                : ''
                        }
                    >
                        {this.getColMeta().map((singleColFieldMeta) => (
                            <Column
                                title={singleColFieldMeta.label}
                                key={singleColFieldMeta.key}
                                {...(this.props.noFilters ||
                                singleColFieldMeta.disable_filter
                                    ? {}
                                    : {
                                          filters:
                                              singleColFieldMeta.options?.map(
                                                  (singleOption) => {
                                                      return {
                                                          ...singleOption,
                                                          text: singleOption.label,
                                                      };
                                                  }
                                              ) || [],
                                          filterSearch: true,
                                          onFilter: (value, record) =>
                                              record[singleColFieldMeta.key] ==
                                              value,
                                      })}
                                render={(text, record, index) => {
                                    let cellFieldMeta;
                                    if (singleColFieldMeta.dynamicMeta) {
                                        cellFieldMeta =
                                            singleColFieldMeta.dynamicMeta(
                                                record,
                                                singleColFieldMeta
                                            );
                                    } else {
                                        cellFieldMeta = {
                                            ...singleColFieldMeta,
                                        };
                                    }

                                    let original_key = cellFieldMeta.key;
                                    cellFieldMeta.label = null;
                                    cellFieldMeta.onChange = (value) => {
                                        record[original_key] =
                                            value?.target?.value || value;
                                        if (this.props.onDataChange) {
                                            this.props.onDataChange(
                                                this.getDataFrTable()
                                            );
                                        }
                                        this.refresh();
                                    };

                                    if (readOnly) {
                                        cellFieldMeta.widgetProps = {
                                            ...(cellFieldMeta.widgetProps ||
                                                {}),
                                            disabled: true,
                                        };
                                    }

                                    let duplicateRecord = { ...record };
                                    if (record[original_key]) {
                                        duplicateRecord[cellFieldMeta.key] =
                                            record[original_key];
                                    }
                                    return (
                                        <div className="gx-px-2">
                                            <CellFormField
                                                cellFieldMeta={cellFieldMeta}
                                                record={duplicateRecord}
                                            />
                                        </div>
                                    );
                                }}
                            />
                        ))}

                        {!hideActionBtn && edittable && !readOnly && (
                            <Column
                                key="edittable_controls"
                                title="Actions"
                                fixed={actionColFixed ? 'right' : undefined}
                                width={110}
                                render={(text, record, index) => {
                                    return (
                                        <div>
                                            <Button
                                                type="link"
                                                className="gx-m-0 gx-text-success"
                                                icon={<PlusCircleFilled />}
                                                onClick={() =>
                                                    this.onEdittableControlClick(
                                                        record,
                                                        index,
                                                        1
                                                    )
                                                }
                                            />
                                            <Button
                                                type="link"
                                                className="gx-m-0 gx-text-danger"
                                                icon={<MinusCircleFilled />}
                                                onClick={() =>
                                                    this.onEdittableControlClick(
                                                        record,
                                                        index,
                                                        -1
                                                    )
                                                }
                                            />
                                        </div>
                                    );
                                }}
                            />
                        )}
                    </Table>
                )}

                {!loading && pagination.total > 0 && (
                    <>
                        <Pagination
                            className="gx-mt-3"
                            {...pagination}
                            onChange={this.handlePaginationChange}
                            size={this.props?.paginationSize || 'default'}
                        />
                        {this.props.noTotal
                            ? null
                            : `Total - ${pagination.total}`}
                    </>
                )}

                {demoMode && (
                    <div className="gx-border gx-border-blue gx-p-1 gx-mt-3">
                        {JSON.stringify(this.getDataFrTable())}
                    </div>
                )}
            </div>
        );
    }
}
