CREATE OR REPLACE FUNCTION public.tms_get_execution_master_list(form_data_ json, filter_ json, search_query text, page_no integer, page_size integer)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
    matching_json json;
    pagination json;
    total integer;
    org_id_ integer;
    usr_id_ uuid;
    filter_search_query text;
    filter_vertical_id integer;
    filter_service_type_id integer[];
begin
	status = false;
	message = 'Internal_error';
	
	org_id_ = form_data_->>'org_id';
    usr_id_ = form_data_->>'usr_id';
    
    -- Extract filters
    filter_vertical_id = (filter_->>'vertical_id')::integer;
    filter_service_type_id = array( select json_array_elements_text(filter_->'service_type_id'))::integer[];
    
    -- Search
	 if search_query != '' then
        filter_search_query = concat('%', search_query, '%');  -- Adding wildcards for partial matching
    else
        filter_search_query := null;  -- Set it to null when there's no search query
    end if;

    -- Get matching data with pagination
    matching_json = array_to_json(array(
        select jsonb_build_object(
            'id',sku.db_id,
            'service_type_name', service_types.title ,
            'service_type_id',service_types.service_type_id ,
            'product_details', CONCAT(sku.product_name, ' (', sku.sku, ')') ,
            'sku_id', sku.db_id,         
            'skill_1',skill_1.skill_name,
            'skill_2',skill_2.skill_name,
            'skill_3',skill_3.skill_name,
            'manpower_1',em.manpower_1,
            'manpower_2',em.manpower_2,
            'manpower_3',em.manpower_3,
            'duration_1',em.duration_1,
            'duration_2',em.duration_2,
            'duration_3',em.duration_3,          
            'full_count', count(sku.db_id) OVER()
        )
   
         FROM cl_tx_category cat
         JOIN cl_tx_product_sku sku 
           ON cat.db_id = sku.category
         JOIN cl_cf_service_types service_types
           ON service_types.service_type_id = ANY(filter_service_type_id)
         left join execution_master as em 
           on em.sku_id = sku.db_id  
         left join cl_tx_skills as skill_1 
           on skill_1.db_id = em.skill_1
         left join cl_tx_skills as skill_2 
           on skill_2.db_id = em.skill_2
         left join cl_tx_skills as skill_3 
           on skill_3.db_id = em.skill_3 
        where (
	             filter_service_type_id is null 
	             or array_length(filter_service_type_id, 1) is null 
	             or sku.service_type  && filter_service_type_id
              )
          and (
	             (filter_search_query is null)
	             or sku.sku ilike filter_search_query
	             or sku.product_name  ilike filter_search_query
              ) 
        GROUP BY service_types.service_type_id, cat.db_id, cat.category_name, sku.db_id,sku.product_name,skill_1.db_id,skill_2.db_id,skill_3.db_id,em.id
        limit page_size
       offset ( (page_no - 1) * page_size )
    ));
    
    -- Get total count
  --  if array_length(matching_json::json[], 1) > 0 then
        total = (matching_json->0->>'full_count')::integer;
 --   else
  --      total = 0;
 --   end if;
    
    -- Build response data
    resp_data = array_to_json(array(
        select jsonb_build_object(
            'id', value->>'id',
            'service_type_name', value->>'service_type_name',
            'sku_id', value->>'sku_id',
            'skill_1',value->>'skill_1',
            'skill_2',value->>'skill_2',
            'skill_3',value->>'skill_3',
            'manpower_1',value->>'manpower_1',
            'manpower_2',value->>'manpower_2',
            'manpower_3',value->>'manpower_3',
            'duration_1',value->>'duration_1',
            'duration_2',value->>'duration_2',
            'duration_3',value->>'duration_3',
            'product_details' ,value->>'product_details',
            'service_type_id',value->>'service_type_id'
        )
        
        from json_array_elements(matching_json) as value
    ));

    pagination = jsonb_build_object('total', total, 'current', page_no);
    resp_data = jsonb_build_object('pagination', pagination, 'data', resp_data);
   
	status = true;
    message = 'success';
   
	return json_build_object('status',status,'code',message,'data',resp_data);			

END;
$function$
;
