import React, { useState, useEffect } from 'react';
import { Input, Select, Button, Card, Space, message } from 'antd';
import EditablePagedApiTable from './index';
import http_utils from '../../../util/http_utils';

const { Search } = Input;
const { Option } = Select;

const ExecutionMasterExample = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [filterObject, setFilterObject] = useState({});
    const [tableData, setTableData] = useState([]);
    const [verticalOptions, setVerticalOptions] = useState([]);
    const [skillOptions, setSkillOptions] = useState([]);
    const [loading, setLoading] = useState(false);

    // Load initial data for dropdowns
    useEffect(() => {
        loadVerticalOptions();
        loadSkillOptions();
    }, []);

    const loadVerticalOptions = () => {
        // Replace with your actual API endpoint
        http_utils.performGetCall(
            '/api/verticals',
            {},
            (response) => {
                setVerticalOptions(response.data || []);
            },
            (error) => {
                console.error('Error loading verticals:', error);
            }
        );
    };

    const loadSkillOptions = () => {
        // Replace with your actual API endpoint
        http_utils.performGetCall(
            '/api/skills',
            {},
            (response) => {
                setSkillOptions(response.data || []);
            },
            (error) => {
                console.error('Error loading skills:', error);
            }
        );
    };

    // Column metadata for execution master table
    const getExecutionMasterColMeta = () => [
        {
            key: 'vertical_name',
            label: 'Vertical',
            widget: 'select',
            options: verticalOptions,
            widgetProps: {
                disabled: true, // Usually not editable in execution master
                showSearch: true,
            },
        },
        {
            key: 'service_type_name',
            label: 'Service Type',
            widget: 'input',
            widgetProps: {
                disabled: true,
            },
        },
        {
            key: 'product_name',
            label: 'Product Name',
            widget: 'input',
            widgetProps: {
                disabled: true,
            },
        },
        {
            key: 'sku_code',
            label: 'SKU Code',
            widget: 'input',
            widgetProps: {
                disabled: true,
            },
        },
        {
            key: 'skill_1',
            label: 'Skill 1',
            widget: 'select',
            options: skillOptions,
            widgetProps: {
                allowClear: true,
                showSearch: true,
                placeholder: 'Select skill 1',
            },
        },
        {
            key: 'manpower_1',
            label: 'Manpower 1',
            widget: 'number',
            widgetProps: {
                min: 0,
                placeholder: 'Enter manpower',
            },
        },
        {
            key: 'duration_1',
            label: 'Duration 1 (min)',
            widget: 'number',
            widgetProps: {
                min: 0,
                placeholder: 'Enter duration in minutes',
            },
        },
        {
            key: 'skill_2',
            label: 'Skill 2',
            widget: 'select',
            options: skillOptions,
            widgetProps: {
                allowClear: true,
                showSearch: true,
                placeholder: 'Select skill 2',
            },
        },
        {
            key: 'manpower_2',
            label: 'Manpower 2',
            widget: 'number',
            widgetProps: {
                min: 0,
                placeholder: 'Enter manpower',
            },
        },
        {
            key: 'duration_2',
            label: 'Duration 2 (min)',
            widget: 'number',
            widgetProps: {
                min: 0,
                placeholder: 'Enter duration in minutes',
            },
        },
        {
            key: 'skill_3',
            label: 'Skill 3',
            widget: 'select',
            options: skillOptions,
            widgetProps: {
                allowClear: true,
                showSearch: true,
                placeholder: 'Select skill 3',
            },
        },
        {
            key: 'manpower_3',
            label: 'Manpower 3',
            widget: 'number',
            widgetProps: {
                min: 0,
                placeholder: 'Enter manpower',
            },
        },
        {
            key: 'duration_3',
            label: 'Duration 3 (min)',
            widget: 'number',
            widgetProps: {
                min: 0,
                placeholder: 'Enter duration in minutes',
            },
        },
    ];

    // Handle search input change
    const handleSearchChange = (value) => {
        setSearchQuery(value);
    };

    // Handle filter changes
    const handleFilterChange = (key, value) => {
        setFilterObject(prev => ({
            ...prev,
            [key]: value,
        }));
    };

    // Handle data changes from the table
    const handleDataChange = (newData) => {
        setTableData(newData);
        console.log('Execution master data changed:', newData);
    };

    // Handle API response changes
    const handleApiRespChange = (apiResponse) => {
        console.log('API response:', apiResponse);
    };

    // Handle row click
    const handleRowClick = (rowData) => {
        console.log('Execution master row clicked:', rowData);
    };

    // Save execution master data
    const handleSaveExecutionMaster = async () => {
        if (!tableData || tableData.length === 0) {
            message.warning('No data to save');
            return;
        }

        setLoading(true);
        try {
            // Prepare data for API
            const saveData = {
                execution_master_data: tableData.map(row => ({
                    id: row.id,
                    vertical_id: row.vertical_id,
                    service_type_id: row.service_type_id,
                    product_id: row.product_id,
                    skill_1: row.skill_1,
                    skill_2: row.skill_2,
                    skill_3: row.skill_3,
                    manpower_1: row.manpower_1,
                    manpower_2: row.manpower_2,
                    manpower_3: row.manpower_3,
                    duration_1: row.duration_1,
                    duration_2: row.duration_2,
                    duration_3: row.duration_3,
                }))
            };

            // Make API call to save data
            http_utils.performPostCall(
                '/api/execution-master/bulk-update', // Replace with your actual endpoint
                saveData,
                (response) => {
                    message.success('Execution master data saved successfully');
                    console.log('Save response:', response);
                },
                (error) => {
                    message.error('Failed to save execution master data');
                    console.error('Save error:', error);
                }
            );
        } catch (error) {
            message.error('An error occurred while saving');
            console.error('Save error:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div style={{ padding: '20px' }}>
            <Card title="Execution Master - Searchable & Editable Table" style={{ marginBottom: '20px' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                    {/* Search and Filter Controls */}
                    <div style={{ display: 'flex', gap: '10px', marginBottom: '20px', flexWrap: 'wrap' }}>
                        <Search
                            placeholder="Search by product name, SKU, or skill..."
                            allowClear
                            style={{ width: 300 }}
                            onSearch={handleSearchChange}
                            onChange={(e) => handleSearchChange(e.target.value)}
                        />
                        
                        <Select
                            placeholder="Filter by Vertical"
                            allowClear
                            style={{ width: 200 }}
                            onChange={(value) => handleFilterChange('vertical_id', value)}
                        >
                            {verticalOptions.map(vertical => (
                                <Option key={vertical.value} value={vertical.value}>
                                    {vertical.label}
                                </Option>
                            ))}
                        </Select>

                        <Select
                            placeholder="Filter by Skill"
                            allowClear
                            style={{ width: 200 }}
                            onChange={(value) => handleFilterChange('skill', value)}
                        >
                            {skillOptions.map(skill => (
                                <Option key={skill.value} value={skill.value}>
                                    {skill.label}
                                </Option>
                            ))}
                        </Select>

                        <Button 
                            type="primary" 
                            onClick={handleSaveExecutionMaster}
                            loading={loading}
                        >
                            Save Changes
                        </Button>
                    </div>

                    {/* The Hybrid Execution Master Table */}
                    <EditablePagedApiTable
                        // API and Search Props
                        dataSourceApi="/api/execution-master/list" // Replace with your actual API endpoint
                        searchQuery={searchQuery}
                        filterObject={filterObject}
                        pageSize={20}
                        onApiRespChange={handleApiRespChange}
                        onRowClick={handleRowClick}
                        
                        // Editable Table Props
                        colMeta={getExecutionMasterColMeta()}
                        edittable={false} // Set to true if you want add/remove functionality
                        onDataChange={handleDataChange}
                        
                        // Table Display Props
                        bordered={true}
                        tableSize="small"
                        overFlowScrollBar={true}
                        
                        // Action Column Props (hidden for execution master)
                        hideActionBtn={true}
                        
                        // Other Props
                        readOnly={false}
                        noFilters={false}
                        paginationSize="small"
                    />
                </Space>
            </Card>

            {/* Instructions */}
            <Card title="Instructions" size="small">
                <ul>
                    <li>Use the search bar to find specific products, SKUs, or skills</li>
                    <li>Filter by vertical or skill to narrow down results</li>
                    <li>Click on any skill, manpower, or duration cell to edit inline</li>
                    <li>Changes are automatically tracked - click "Save Changes" to persist</li>
                    <li>The table supports pagination for large datasets</li>
                </ul>
            </Card>
        </div>
    );
};

export default ExecutionMasterExample;
