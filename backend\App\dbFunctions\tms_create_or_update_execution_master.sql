CREATE OR REPLACE FUNCTION public.tms_create_or_update_execution_master(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    status boolean;
    message text;
    resp_data json;
    affected_rows integer;
    ins_id integer;

    -- Extract metadata from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;

    -- Extract main form fields
    vertical_id_ INT;
    v_service_type_id INT;
    v_execution_mode TEXT;

    -- Variables for iterating through the brand table data
    row_data json;
    row_id text;
    srvc_type int;
    category_id int;
    category_name text;
    product_id int;
    product_name text;
    sku_code text;
    skill_1_ int;
    skill_2_ int;
    skill_3_ int;
    manpower_1_ text;
    manpower_2_ text;
    manpower_3_ text;
    duration_1_ text;
    duration_2_ text;
    duration_3_ text;
    input_table_id uuid;
    key text;
BEGIN
    message = 'Internal_error';
    status = false;        
    
    -- Extract general data from form_data_
    usr_id_ := form_data_->>'usr_id';
    org_id_ := (form_data_->>'org_id')::int;
    vertical_id_ := (form_data_->>'vertical_list')::int;    
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';


    -- Extract brand table data (product and skill details)
    FOR row_data IN 
        SELECT * FROM json_array_elements(form_data_->'execution_master_table_meta')
        
    loop
	    raise notice 'row_data %',row_data;
        -- Extract individual values for each row
        row_id := row_data->>'row_id';
        srvc_type := (row_data->>'service_type_id')::int;
      --  category_id := (row_data->>'category_id')::int;
     --   category_name := row_data->>'category_name';
        product_id := (row_data->>'sku_id')::int;
        product_name := row_data->>'product_name';
        sku_code := row_data->>'sku_code';
        skill_1_ := NULLIF(row_data->>'skill_1', '')::int;  -- Handling empty string for skill_1
        skill_2_ := NULLIF(row_data->>'skill_2', '')::int;  -- Handling empty string for skill_2
        skill_3_ := NULLIF(row_data->>'skill_3', '')::int;  -- Handling empty string for skill_3

        manpower_1_ := row_data->>'manpower_1';
        manpower_2_ := row_data->>'manpower_2';
        manpower_3_ := row_data->>'manpower_3';
        duration_1_ := row_data->>'duration_1';
        duration_2_ := row_data->>'duration_2';
        duration_3_ := row_data->>'duration_3';
        input_table_id := (row_data->>'input_table_id')::uuid;
        key := row_data->>'key';

        -- Check if the record already exists
        SELECT id INTO ins_id
        FROM public.execution_master
        WHERE org_id = org_id_ 
          AND vertical_id = vertical_id_
          AND service_type_id = srvc_type
          AND sku_id = product_id
        LIMIT 1;

        -- If the record exists, update it, else insert new record
        IF FOUND THEN
            -- Update the existing record
            UPDATE public.execution_master
            SET skill_1 = skill_1_,
                skill_2 = skill_2_,
                skill_3 = skill_3_,
                manpower_1 = manpower_1_,
                manpower_2 = manpower_2_,
                manpower_3 = manpower_3_,
                duration_1 = duration_1_,
                duration_2 = duration_2_,
                duration_3 = duration_3_,
                c_by = usr_id_,
                c_meta = row(ip_address_,user_agent_,now() at time zone 'utc'),
                u_by = usr_id_,
                u_meta = row(ip_address_,user_agent_,now() at time zone 'utc'),
                form_data = form_data_                
            WHERE id = ins_id
            RETURNING id INTO ins_id;
            
        ELSE
            -- Insert new record
            INSERT INTO public.execution_master(
                org_id, vertical_id, service_type_id, sku_id, skill_1, skill_2, skill_3,
                manpower_1, manpower_2, manpower_3, duration_1, duration_2, duration_3,
                c_by, c_meta, form_data
            )
            VALUES (
                org_id_, vertical_id_, srvc_type, product_id, skill_1_, skill_2_, skill_3_,
                manpower_1_, manpower_2_, manpower_3_, duration_1_, duration_2_, duration_3_,
                usr_id_, row(ip_address_,user_agent_,now() at time zone 'utc'),
                form_data_
            ) RETURNING id INTO ins_id;
        END IF;

        -- Return the affected row's id as part of the response
        resp_data := jsonb_set(resp_data::jsonb, '{affected_rows}', to_jsonb(ins_id), true);
        status := true;
    END LOOP;

    -- Return the response with status and the inserted/updated record ID
    message := 'success';
    RETURN jsonb_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
