CREATE OR REPLACE FUNCTION public.tms_save_execution_master_row(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	-- 	Bare minimums
	status boolean;
	message text;
	resp_data json;
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    vertical_id_ integer;
    service_type_id_ integer;
    category_id_ integer;
    product_id_ integer;
    product_name_ text;
    sku_code_ text;
    category_name_ text;
    skill_1_ text;
    skill_2_ text;
    skill_3_ text;
    manpower_1_ integer;
    manpower_2_ integer;
    manpower_3_ integer;
    duration_1_ integer;
    duration_2_ integer;
    duration_3_ integer;
    existing_id integer;
begin
	status = false;
	message = 'Internal_error';
	
	-- Extract form data
	org_id_ = form_data_->>'org_id';
    usr_id_ = form_data_->>'usr_id';
    ip_address_ = form_data_->>'ip_address';
    user_agent_ = form_data_->>'user_agent';
    vertical_id_ = (form_data_->>'vertical_id')::integer;
    service_type_id_ = (form_data_->>'service_type_id')::integer;
    category_id_ = (form_data_->>'category_id')::integer;
    product_id_ = (form_data_->>'product_id')::integer;
    product_name_ = form_data_->>'product_name';
    sku_code_ = form_data_->>'sku_code';
    category_name_ = form_data_->>'category_name';
    skill_1_ = COALESCE(form_data_->>'skill_1', '');
    skill_2_ = COALESCE(form_data_->>'skill_2', '');
    skill_3_ = COALESCE(form_data_->>'skill_3', '');
    manpower_1_ = COALESCE((form_data_->>'manpower_1')::integer, 0);
    manpower_2_ = COALESCE((form_data_->>'manpower_2')::integer, 0);
    manpower_3_ = COALESCE((form_data_->>'manpower_3')::integer, 0);
    duration_1_ = COALESCE((form_data_->>'duration_1')::integer, 0);
    duration_2_ = COALESCE((form_data_->>'duration_2')::integer, 0);
    duration_3_ = COALESCE((form_data_->>'duration_3')::integer, 0);

    -- Check if record already exists
    SELECT id INTO existing_id
    FROM cl_tx_execution_master
    WHERE org_id = org_id_
    AND vertical_id = vertical_id_
    AND service_type_id = service_type_id_
    AND category_id = category_id_
    AND product_id = product_id_;

    IF existing_id IS NOT NULL THEN
        -- Update existing record
        UPDATE cl_tx_execution_master
        SET 
            product_name = product_name_,
            sku_code = sku_code_,
            category_name = category_name_,
            skill_1 = skill_1_,
            skill_2 = skill_2_,
            skill_3 = skill_3_,
            manpower_1 = manpower_1_,
            manpower_2 = manpower_2_,
            manpower_3 = manpower_3_,
            duration_1 = duration_1_,
            duration_2 = duration_2_,
            duration_3 = duration_3_,
            updated_at = now() at time zone 'utc'
        WHERE id = existing_id;
        
        resp_data := json_build_object('id', existing_id, 'action', 'updated');
    ELSE
        -- Insert new record
        INSERT INTO cl_tx_execution_master (
            org_id, vertical_id, service_type_id, category_id, product_id,
            product_name, sku_code, category_name,
            skill_1, skill_2, skill_3,
            manpower_1, manpower_2, manpower_3,
            duration_1, duration_2, duration_3,
            created_at, updated_at
        ) VALUES (
            org_id_, vertical_id_, service_type_id_, category_id_, product_id_,
            product_name_, sku_code_, category_name_,
            skill_1_, skill_2_, skill_3_,
            manpower_1_, manpower_2_, manpower_3_,
            duration_1_, duration_2_, duration_3_,
            now() at time zone 'utc', now() at time zone 'utc'
        ) RETURNING id INTO existing_id;
        
        resp_data := json_build_object('id', existing_id, 'action', 'inserted');
    END IF;

    status := true;
    message := 'Success';

    return json_build_object(
        'status', status,
        'message', message,
        'data', resp_data
    );

EXCEPTION
    WHEN OTHERS THEN
        return json_build_object(
            'status', false,
            'message', SQLERRM,
            'data', null
        );
end;
$function$;
