var sampleOperationResp = require('../../utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../../utils/db_resp');
const pagination_filters_utils = require('../../utils/pagination_filters_utils');
const users_model = require('../../users_model');

class execution_master_model {
    createOrUpdate(query) {
        console.log('Query rxd : ', query);
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            // Process execution_master_table_meta if it exists
            if (
                query.execution_master_table_meta &&
                Array.isArray(query.execution_master_table_meta)
            ) {
                // Create a modified query that includes table data processing
                const processedQuery = {
                    ...query,
                    table_data: query.execution_master_table_meta.filter(
                        (row) =>
                            row.row_id &&
                            (row.skill_1 ||
                                row.skill_2 ||
                                row.skill_3 ||
                                row.manpower_1 ||
                                row.manpower_2 ||
                                row.manpower_3 ||
                                row.duration_1 ||
                                row.duration_2 ||
                                row.duration_3)
                    ),
                };
                query = processedQuery;
            }

            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('form_data=>>>>>>>>>>>>>>>>create', form_data);
            this.db.tms_create_or_update_execution_master(form_data).then(
                (res) => {
                    var customFieldResp = new db_resp(
                        res[0].tms_create_or_update_execution_master
                    );

                    if (!customFieldResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(customFieldResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getViewData(query, entry_id = 0, brand_id = 0) {
        console.log('Query rxd : ', query);
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['vertical_id'] = entry_id;
            query['brand_id'] = brand_id;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('form_data', form_data);
            this.db.tms_get_view_data_execution(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_view_data_execution
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
            return;
        });
    }

    getSingleEntry(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db
                .tms_get_execution_master_single_entry(form_data, entry_id)
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_get_execution_master_single_entry
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getByVerticalAndBrand(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db.tms_get_execution_master_by_vertical_brand(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_get_execution_master_by_vertical_brand
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getExecutionMasterList(query) {
        return new Promise((resolve, reject) => {
            const { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);

            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            var form_data = JSON.stringify(query);
            console.log('form_data---------------', form_data, filters_);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db
                .tms_get_execution_master_list(
                    form_data,
                    filters_,
                    search_query,
                    page_no_,
                    page_size_
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_get_execution_master_list
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                //return static data for testing

                                //
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
    getFreshInstance(model) {
        const clonedInstance = new execution_master_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new execution_master_model();
